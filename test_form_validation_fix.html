<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单验证错误处理修复</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .comparison-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-section {
            flex: 1;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .comparison-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        .problem {
            background-color: #fff2f0;
            border-left: 4px solid #ff4d4f;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .solution {
            background-color: #f6ffed;
            border-left: 4px solid #52c41a;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .code-block {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 16px;
            margin: 10px 0;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .error-demo {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            border-radius: 4px;
            padding: 12px;
            margin: 10px 0;
            font-family: monospace;
            color: #cf1322;
        }
        .success-demo {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 12px;
            margin: 10px 0;
            font-family: monospace;
            color: #389e0d;
        }
        .form-demo {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
            margin: 10px 0;
            background: #fafafa;
        }
        .form-item {
            margin-bottom: 16px;
        }
        .form-item label {
            display: block;
            margin-bottom: 4px;
            font-weight: 600;
        }
        .form-item input, .form-item textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-item.error input, .form-item.error textarea {
            border-color: #ff4d4f;
        }
        .error-message {
            color: #ff4d4f;
            font-size: 12px;
            margin-top: 4px;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: #1890ff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .btn:disabled {
            background: #f5f5f5;
            color: #bfbfbf;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>表单验证错误处理修复</h1>
    
    <div class="problem">
        <h4>🚨 问题描述</h4>
        <p>当用户在拒绝转正或延期弹窗中没有填写必填字段时，点击提交按钮会显示错误的错误信息：</p>
        <div class="error-demo">
            [Error] 拒绝转正失败: Object<br>
            [Error] 待定处理失败: Object
        </div>
        <p>这是因为表单验证失败时，<code>validateFields()</code> 抛出的验证错误对象被当作普通错误处理，导致显示 "Object" 而不是具体的验证错误信息。</p>
    </div>

    <div class="comparison-container">
        <div class="comparison-section">
            <h3>❌ 修复前的错误处理</h3>
            
            <div class="code-block">
try {
    const values = await rejectForm.validateFields();
    // ... 提交逻辑
} catch (error) {
    console.error('拒绝转正失败:', error);
    message.error(error.message || '拒绝转正失败，请重试');
}
            </div>
            
            <div class="form-demo">
                <h4>模拟场景：用户未填写拒绝理由</h4>
                <div class="form-item error">
                    <label>拒绝理由 *</label>
                    <textarea placeholder="请详细说明拒绝转正的理由..." rows="3"></textarea>
                    <div class="error-message">请输入拒绝理由</div>
                </div>
                <button class="btn" onclick="showOldError()">确认拒绝（旧版本）</button>
                
                <div id="oldErrorResult" style="margin-top: 10px;"></div>
            </div>
            
            <p><strong>问题：</strong></p>
            <ul>
                <li>验证错误对象包含 <code>errorFields</code> 属性</li>
                <li><code>error.message</code> 为 undefined</li>
                <li>最终显示 "Object" 而不是有意义的错误信息</li>
                <li>用户无法理解具体的错误原因</li>
            </ul>
        </div>

        <div class="comparison-section">
            <h3>✅ 修复后的错误处理</h3>
            
            <div class="code-block">
try {
    const values = await rejectForm.validateFields();
    // ... 提交逻辑
} catch (error) {
    console.error('拒绝转正失败:', error);
    
    // 检查是否是表单验证错误
    if (error.errorFields && error.errorFields.length > 0) {
        // 表单验证失败，不显示错误消息，让表单自己显示验证错误
        return;
    }
    
    // 网络请求或其他错误
    message.error(error.message || '拒绝转正失败，请重试');
}
            </div>
            
            <div class="form-demo">
                <h4>模拟场景：用户未填写拒绝理由</h4>
                <div class="form-item error">
                    <label>拒绝理由 *</label>
                    <textarea placeholder="请详细说明拒绝转正的理由..." rows="3"></textarea>
                    <div class="error-message">请输入拒绝理由</div>
                </div>
                <button class="btn" onclick="showNewError()">确认拒绝（新版本）</button>
                
                <div id="newErrorResult" style="margin-top: 10px;"></div>
            </div>
            
            <p><strong>改进：</strong></p>
            <ul>
                <li>区分表单验证错误和网络请求错误</li>
                <li>表单验证失败时不显示额外的错误消息</li>
                <li>让 Ant Design 表单组件自己处理验证错误显示</li>
                <li>只有真正的网络或业务错误才显示 message.error</li>
            </ul>
        </div>
    </div>

    <div class="solution">
        <h4>✅ 解决方案总结</h4>
        <p>修复了拒绝转正和延期处理的表单验证错误处理逻辑：</p>
        <ol>
            <li><strong>识别验证错误：</strong>检查 <code>error.errorFields</code> 属性来判断是否为表单验证错误</li>
            <li><strong>静默处理：</strong>表单验证失败时直接返回，不显示额外的错误消息</li>
            <li><strong>保留原有功能：</strong>网络请求错误和其他业务错误仍然正常显示</li>
            <li><strong>用户体验：</strong>用户只会看到表单字段下方的验证错误提示，不会看到混乱的 "Object" 错误</li>
        </ol>
    </div>

    <script>
        function showOldError() {
            const resultDiv = document.getElementById('oldErrorResult');
            resultDiv.innerHTML = `
                <div class="error-demo">
                    <strong>控制台错误：</strong><br>
                    [Error] 拒绝转正失败: Object<br>
                    errorFields: [{name: ["reason"], errors: ["请输入拒绝理由"], warnings: []}]<br>
                    <br>
                    <strong>用户看到的错误消息：</strong><br>
                    ❌ 拒绝转正失败，请重试
                </div>
            `;
        }

        function showNewError() {
            const resultDiv = document.getElementById('newErrorResult');
            resultDiv.innerHTML = `
                <div class="success-demo">
                    <strong>控制台日志：</strong><br>
                    [Log] 拒绝转正失败: [ValidationError Object]<br>
                    (表单验证错误，已静默处理)<br>
                    <br>
                    <strong>用户体验：</strong><br>
                    ✅ 只显示表单字段下方的验证错误提示<br>
                    ✅ 不显示额外的混乱错误消息<br>
                    ✅ 用户清楚知道需要填写拒绝理由
                </div>
            `;
        }
    </script>
</body>
</html>
