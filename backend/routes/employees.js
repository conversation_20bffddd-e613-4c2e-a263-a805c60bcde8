const express = require('express');
const router = express.Router();
const authMiddleware = require('../authMiddleware');
const mongoose = require('mongoose');
router.use(authMiddleware);
const Employee = require('../models/Employee');
const moment = require('moment');

// 获取所有员工
router.get('/', async (req, res) => {
    try {
        const { fields } = req.query;
        const projection = {};

        if (fields) {
            fields.split(',').forEach(field => {
                projection[field] = 1;
            });
        }

        const employees = await Employee.find({}, projection);
        res.json(employees);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 获取所有试用期员工列表 - 必须在 /:id 路由之前
router.get('/probation-reminders', async (req, res) => {
    try {
        const today = moment();

        // 查找所有试用期员工
        const probationEmployees = await Employee.find({
            workType: '试用',
            status: '在职',
            probationEndDate: { $exists: true, $ne: null, $ne: '' }
        });

        // 为每个员工添加状态信息
        const employeesWithStatus = probationEmployees.map(employee => {
            const endDate = moment(employee.probationEndDate);
            const diffDays = endDate.diff(today, 'days');

            let probationStatus = 'normal';
            let statusText = '正常';
            let daysInfo = '';

            if (diffDays < 0) {
                probationStatus = 'expired';
                statusText = '已过期';
                daysInfo = `已过期 ${Math.abs(diffDays)} 天`;
            } else if (diffDays <= 7) {
                probationStatus = 'upcoming';
                statusText = '即将到期';
                daysInfo = `还有 ${diffDays} 天到期`;
            } else {
                daysInfo = `还有 ${diffDays} 天到期`;
            }

            return {
                ...employee.toObject(),
                probationStatus,
                statusText,
                daysInfo,
                daysRemaining: diffDays > 0 ? diffDays : 0,
                daysOverdue: diffDays < 0 ? Math.abs(diffDays) : 0
            };
        });

        // 按到期日期排序，最紧急的在前
        employeesWithStatus.sort((a, b) => {
            const dateA = moment(a.probationEndDate);
            const dateB = moment(b.probationEndDate);
            return dateA.diff(dateB);
        });

        res.json(employeesWithStatus);
    } catch (error) {
        console.error('获取试用期员工失败:', error);
        res.status(500).json({ message: '获取试用期员工失败', error: error.message });
    }
});

// 获取单个员工
router.get('/:id', async (req, res) => {
    try {
        const idParam = req.params.id;
        let query = {};

        console.log(`[员工API] 收到查询员工请求，idParam: ${idParam}`);

        // 检查idParam是否是有效的MongoDB ObjectId
        const isValidObjectId = mongoose.Types.ObjectId.isValid(idParam);
        console.log(`[员工API] idParam '${idParam}' 是有效的ObjectId吗？${isValidObjectId}`);

        if (isValidObjectId) {
            // 如果是有效的ObjectId，使用$or查询同时匹配_id和employeeId
            query = {
                $or: [
                    { employeeId: idParam },
                    { _id: idParam }
                ]
            };
        } else {
            // 如果不是有效的ObjectId，只通过employeeId查找
            query = { employeeId: idParam };
        }

        console.log(`[员工API] 使用查询条件: ${JSON.stringify(query)}`);

        const employee = await Employee.findOne(query);

        if (employee) {
            console.log(`[员工API] 找到员工: ${employee.employeeId} - ${employee.name}`);
            res.json(employee);
        } else {
            console.log(`[员工API] 未找到员工: ${idParam}`);
            res.status(404).json({ message: '未找到该员工' });
        }
    } catch (error) {
        console.error('获取单个员工数据错误:', error);
        res.status(500).json({ message: error.message });
    }
});

// 添加新员工
router.post('/', async (req, res) => {
    const employee = new Employee(req.body);
    try {
        const employeeData = req.body;

        // 如果最高学历是"与第一学历相同"，转换为实际值
        if (employeeData.finalEducation === 'same') {
            employeeData.finalEducation = employeeData.firstEducation;
        }
        console.log('接收到的数据:', req.body); // 添加日志
        const newEmployee = await employee.save();
        res.status(201).json(newEmployee);
    } catch (error) {
        console.error('保存员工数据错误:', error); // 添加错误日志
        res.status(400).json({
            message: error.message,
            details: error.errors || '保存失败'
        });
    }
});

// 更新员工信息
router.put('/:id', async (req, res) => {
    try {
        const employeeData = req.body;
        // 如果最高学历是"与第一学历相同"，转换为实际值
        if (employeeData.finalEducation === 'same') {
            employeeData.finalEducation = employeeData.firstEducation;
        }
        // 只整体更新emergencyContact，避免与子字段冲突
        if (employeeData.emergencyContact) {
            // 移除所有emergencyContact.xxx的平铺字段，防止冲突
            Object.keys(employeeData).forEach(key => {
                if (key.startsWith('emergencyContact.') && key !== 'emergencyContact') {
                    delete employeeData[key];
                }
            });
        }
        console.log('更新前的数据:', await Employee.findById(req.params.id)); // 添加日志
        console.log('接收到的更新数据:', employeeData); // 添加详细日志
        const employee = await Employee.findByIdAndUpdate(
            req.params.id,
            employeeData,
            {
                new: true,
                runValidators: true,
                returnDocument: 'after'
            }
        );
        console.log('更新后的数据:', employee); // 添加日志
        if (!employee) {
            return res.status(404).json({ message: '未找到该员工' });
        }
        res.json(employee);
    } catch (error) {
        console.error('更新员工数据错误:', error); // 添加错误日志
        res.status(400).json({
            message: error.message,
            details: error.errors || '更新失败'
        });
    }
});

// 删除员工 - 完全重写，避免ObjectId转换问题
router.delete('/:id', async (req, res) => {
    try {
        const idOrEmployeeId = req.params.id;
        console.log(`收到删除员工请求: ${idOrEmployeeId}`);

        // 创建查询条件，同时支持ID和工号
        let query = {};

        // 检查是否是有效的MongoDB ObjectId
        const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(idOrEmployeeId);

        if (isValidObjectId) {
            // 如果是有效的ObjectId，使用$or查询同时匹配_id和employeeId
            query = {
                $or: [
                    { employeeId: idOrEmployeeId },
                    { _id: idOrEmployeeId }
                ]
            };
        } else {
            // 如果不是有效的ObjectId，只通过employeeId查找
            query = { employeeId: idOrEmployeeId };
        }

        console.log('使用查询条件:', JSON.stringify(query));

        // 查找员工
        const employee = await Employee.findOne(query);

        if (!employee) {
            console.log(`未找到要删除的员工: ${idOrEmployeeId}`);
            return res.status(404).json({ message: '未找到该员工' });
        }

        console.log(`找到要删除的员工: ID=${employee._id}, 工号=${employee.employeeId}, 姓名=${employee.name}`);

        // 先删除该员工的薪资记录
        const Salary = require('../models/Salary');

        // 通过employeeId删除薪资记录
        const deleteByIdResult = await Salary.deleteMany({ employeeId: employee.employeeId });
        console.log(`通过employeeId删除薪资记录结果: 删除了 ${deleteByIdResult.deletedCount} 条记录`);

        // 通过姓名删除薪资记录 (以防employeeId不匹配但姓名匹配)
        if (employee.name) {
            const deleteByNameResult = await Salary.deleteMany({ name: employee.name });
            console.log(`通过姓名删除薪资记录结果: 删除了 ${deleteByNameResult.deletedCount} 条记录`);
        }

        // 删除员工记录
        const deleteResult = await Employee.deleteMany({ employeeId: employee.employeeId });
        console.log(`删除员工结果: 删除了 ${deleteResult.deletedCount} 条记录`);

        // 检查是否还有同工号的员工
        const remainingEmployees = await Employee.find({ employeeId: employee.employeeId });

        if (remainingEmployees.length > 0) {
            console.log(`警告：删除后仍有 ${remainingEmployees.length} 个相同工号的员工记录，尝试强制删除`);

            // 强制删除所有同工号的员工
            for (const emp of remainingEmployees) {
                try {
                    await Employee.deleteOne({ _id: emp._id });
                    console.log(`强制删除员工 ${emp._id} 成功`);
                } catch (e) {
                    console.error(`强制删除员工 ${emp._id} 失败:`, e);
                }
            }
        }

        res.json({
            message: '员工及其薪资记录已删除',
            employeeId: employee.employeeId,
            name: employee.name
        });
    } catch (error) {
        console.error('删除员工时出错:', error);
        res.status(500).json({ message: error.message });
    }
});
// 添加工号查重接口
router.get('/check/:employeeId', async (req, res) => {
    try {
        const employee = await Employee.findOne({ employeeId: req.params.employeeId });
        res.json({ exists: !!employee });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 确认员工转正
router.post('/:id/regularization', async (req, res) => {
    try {
        const { id } = req.params;
        const { regularizationDate, newPosition, newDepartment, newSubDepartment, remarks } = req.body;

        // 验证员工是否存在
        const employee = await Employee.findById(id);
        if (!employee) {
            return res.status(404).json({ message: '员工不存在' });
        }

        // 验证员工是否为试用期员工
        if (employee.workType !== '试用') {
            return res.status(400).json({ message: '该员工不是试用期员工' });
        }

        // 更新员工信息
        const updateData = {
            workType: '全职',  // 转正后工作性质改为全职，与EmployeeForm中的选项保持一致
            regularizationDate: regularizationDate,
            position: newPosition,
            department: newDepartment,
            subDepartment: newSubDepartment || '',
            // 可以添加转正备注字段
            regularizationRemarks: remarks || ''
        };

        const updatedEmployee = await Employee.findByIdAndUpdate(
            id,
            updateData,
            { new: true, runValidators: true }
        );

        console.log(`员工 ${employee.name} (${employee.employeeId}) 转正成功`);
        res.json({
            message: '员工转正成功',
            employee: updatedEmployee
        });
    } catch (error) {
        console.error('员工转正失败:', error);
        res.status(500).json({ message: '员工转正失败', error: error.message });
    }
});

// 拒绝员工转正
router.post('/:id/reject-regularization', async (req, res) => {
    try {
        const { id } = req.params;
        const { reason } = req.body;

        // 验证员工是否存在
        const employee = await Employee.findById(id);
        if (!employee) {
            return res.status(404).json({ message: '员工不存在' });
        }

        // 验证员工是否为试用期员工
        if (employee.workType !== '试用') {
            return res.status(400).json({ message: '该员工不是试用期员工' });
        }

        // 更新员工信息 - 拒绝转正，设为离职
        const updateData = {
            workType: '离职',
            status: '离职',
            leaveDate: new Date().toISOString().split('T')[0], // 当前日期作为离职日期
            leaveReason: '试用期不合格',
            regularizationRemarks: `拒绝转正理由：${reason}`
        };

        const updatedEmployee = await Employee.findByIdAndUpdate(
            id,
            updateData,
            { new: true }
        );

        console.log(`员工 ${employee.name} (${employee.employeeId}) 转正被拒绝`);
        res.json({ message: '员工转正拒绝处理成功', employee: updatedEmployee });
    } catch (error) {
        console.error('拒绝转正失败:', error);
        res.status(500).json({ message: '拒绝转正失败', error: error.message });
    }
});

// 员工转正待定处理
router.post('/:id/pending-regularization', async (req, res) => {
    try {
        const { id } = req.params;
        const { reason, pendingUntil } = req.body;

        // 验证员工是否存在
        const employee = await Employee.findById(id);
        if (!employee) {
            return res.status(404).json({ message: '员工不存在' });
        }

        // 验证员工是否为试用期员工
        if (employee.workType !== '试用') {
            return res.status(400).json({ message: '该员工不是试用期员工' });
        }

        // 更新员工信息 - 延长试用期
        const updateData = {
            probationEndDate: pendingUntil, // 延长试用期截止日期
            regularizationRemarks: `转正待定理由：${reason}，延期至：${pendingUntil}`
        };

        const updatedEmployee = await Employee.findByIdAndUpdate(
            id,
            updateData,
            { new: true }
        );

        console.log(`员工 ${employee.name} (${employee.employeeId}) 转正待定处理，延期至：${pendingUntil}`);
        res.json({ message: '员工转正待定处理成功', employee: updatedEmployee });
    } catch (error) {
        console.error('待定处理失败:', error);
        res.status(500).json({ message: '待定处理失败', error: error.message });
    }
});

module.exports = router;
