<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>试用期截止日期修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select {
            padding: 5px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .result {
            background-color: #f0f8ff;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            border-left: 4px solid #007bff;
        }
        .expected {
            background-color: #f0fff0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <h1>试用期截止日期修复测试</h1>
    
    <div class="test-case">
        <h3>测试场景：修改现有员工为试用期状态</h3>
        <p><strong>问题描述：</strong>当将现有员工的工作状态修改为"试用"，并设置入职日期和试用期月数时，试用期截止日期没有自动更新。</p>
        
        <div class="form-group">
            <label>工作状态：</label>
            <select id="workType" onchange="updateProbationEndDate()">
                <option value="全职">全职</option>
                <option value="试用">试用</option>
                <option value="兼职">兼职</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>入职日期：</label>
            <input type="date" id="entryDate" onchange="updateProbationEndDate()" />
        </div>
        
        <div class="form-group">
            <label>试用期（月）：</label>
            <select id="probationMonths" onchange="updateProbationEndDate()">
                <option value="无">无</option>
                <option value="1">1个月</option>
                <option value="3">3个月</option>
                <option value="6">6个月</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>试用期截止日期：</label>
            <input type="date" id="probationEndDate" readonly style="background-color: #f5f5f5;" />
        </div>
        
        <div class="result">
            <strong>计算结果：</strong>
            <div id="calculationResult">请选择工作状态为"试用"并设置入职日期和试用期月数</div>
        </div>
        
        <div class="expected">
            <strong>期望行为：</strong>
            <ul>
                <li>当工作状态选择"试用"时，试用期截止日期字段应该可以被计算</li>
                <li>当入职日期或试用期月数改变时，试用期截止日期应该自动更新</li>
                <li>试用期截止日期 = 入职日期 + 试用期月数</li>
                <li>司龄应该显示"试用期内"（如果当前日期在试用期内）</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟 EmployeeForm.js 中的 calculateProbationEndDate 函数
        function calculateProbationEndDate(entryDate, months) {
            if (!entryDate || !months || months === '无') return '';
            const date = new Date(entryDate);
            date.setMonth(date.getMonth() + parseInt(months));
            // 确保返回的日期格式为 YYYY-MM-DD
            return date.getFullYear() + '-' +
                   String(date.getMonth() + 1).padStart(2, '0') + '-' +
                   String(date.getDate()).padStart(2, '0');
        }

        // 模拟 calculateSeniority 函数
        function calculateSeniority(entryDate, workType, probationEndDate) {
            if (!entryDate) return '-';
            const today = new Date();
            const startDate = new Date(entryDate);

            // 先判断是否是未来日期
            if (startDate > today) {
                return '未入职';
            }

            // 再判断试用期
            if (workType === '试用' && probationEndDate) {
                const probationEnd = new Date(probationEndDate);
                if (today < probationEnd) {
                    return '试用期内';
                }
            }

            // 计算司龄
            const diffTime = today - startDate;
            const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
            const years = Math.floor(diffDays / 365);
            const months = Math.floor((diffDays % 365) / 30);
            const days = diffDays % 30;

            // 格式化输出
            let result = '';
            if (years > 0) {
                result += `${years}年`;
            }
            if (months > 0) {
                result += `${months}个月`;
            }
            if (days > 0 && years === 0 && months === 0) {
                result += `${days}天`;
            }

            return result || '不满1天';
        }

        function updateProbationEndDate() {
            const workType = document.getElementById('workType').value;
            const entryDate = document.getElementById('entryDate').value;
            const probationMonths = document.getElementById('probationMonths').value;
            
            let probationEndDate = '';
            let seniority = '';
            let resultText = '';

            if (workType === '试用') {
                // 启用试用期相关字段
                document.getElementById('probationMonths').disabled = false;
                
                if (entryDate && probationMonths && probationMonths !== '无') {
                    probationEndDate = calculateProbationEndDate(entryDate, probationMonths);
                    document.getElementById('probationEndDate').value = probationEndDate;
                    
                    seniority = calculateSeniority(entryDate, workType, probationEndDate);
                    
                    resultText = `✅ 试用期截止日期已自动计算：${probationEndDate}<br>`;
                    resultText += `✅ 司龄计算结果：${seniority}`;
                } else {
                    resultText = '⚠️ 请设置入职日期和试用期月数以计算截止日期';
                }
            } else {
                // 禁用试用期相关字段
                document.getElementById('probationMonths').disabled = true;
                document.getElementById('probationMonths').value = '无';
                document.getElementById('probationEndDate').value = '';
                
                if (entryDate) {
                    seniority = calculateSeniority(entryDate, workType, '');
                    resultText = `✅ 非试用期员工，司龄：${seniority}`;
                } else {
                    resultText = '请设置入职日期';
                }
            }

            document.getElementById('calculationResult').innerHTML = resultText;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认值进行测试
            document.getElementById('entryDate').value = new Date().toISOString().split('T')[0];
            updateProbationEndDate();
        });
    </script>
</body>
</html>
