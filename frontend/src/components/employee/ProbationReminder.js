import React, { useState, useEffect, useCallback } from 'react';
import { Modal, Table, Button, Tag, message, Form, Input, Select } from 'antd';
import { ExclamationCircleOutlined, CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import config from '../../config';
import './ProbationReminder.css';
import '../common/TextLinkStyles.css';
import '../employee/DatePickerStyles.css';

const { TextArea } = Input;
const { Option } = Select;

const ProbationReminder = ({ visible, onClose, onRefresh }) => {
    const [loading, setLoading] = useState(false);
    const [probationEmployees, setProbationEmployees] = useState([]);
    const [confirmModalVisible, setConfirmModalVisible] = useState(false);
    const [rejectModalVisible, setRejectModalVisible] = useState(false);
    const [pendingModalVisible, setPendingModalVisible] = useState(false);
    const [selectedEmployee, setSelectedEmployee] = useState(null);
    const [form] = Form.useForm();
    const [rejectForm] = Form.useForm();
    const [pendingForm] = Form.useForm();
    const [isSubmitting, setIsSubmitting] = useState(false); // 防止重复提交

    // 拖动相关状态
    const [isDragging, setIsDragging] = useState(false);
    const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
    const [position, setPosition] = useState({ x: 0, y: 0 });

    // 拖动处理函数
    const handleMouseMove = useCallback((e) => {
        if (isDragging) {
            const newX = e.clientX - dragOffset.x;
            const newY = e.clientY - dragOffset.y;

            // 限制在视窗范围内
            const maxX = window.innerWidth - 800; // Modal宽度
            const maxY = window.innerHeight - 600; // 估计Modal高度

            setPosition({
                x: Math.max(0, Math.min(newX, maxX)),
                y: Math.max(0, Math.min(newY, maxY))
            });
        }
    }, [isDragging, dragOffset]);

    const handleMouseUp = useCallback(() => {
        setIsDragging(false);
    }, []);

    // 开始拖动
    const handleMouseDown = (e) => {
        setIsDragging(true);
        setDragOffset({
            x: e.clientX - position.x,
            y: e.clientY - position.y
        });
    };

    // 居中位置
    const centerPosition = useCallback(() => {
        setPosition({
            x: (window.innerWidth - 800) / 2,
            y: (window.innerHeight - 600) / 2
        });
    }, []);



    // 处理日期输入的实时匹配 - 适配Ant Design Form
    const handleDateInputMatch = (value, fieldName, formInstance) => {
        // 尝试匹配年份
        if (value.length >= 4) {
            const yearStr = value.substring(0, 4);
            const year = parseInt(yearStr, 10);

            // 检查是否是有效年份
            if (!isNaN(year) && year >= 1900 && year <= 2100) {
                let month = 1;
                let day = 1;

                // 检查是否有分隔符
                const hasSeparator = value.length > 4 && (value[4] === '-' || value[4] === '/');

                // 尝试匹配月份 (处理 YYYY-M, YYYY-MM, YYYY/M, YYYY/MM 或 YYYYMM 格式)
                if (value.length >= 5) {
                    let monthStr;

                    if (hasSeparator) {
                        // 处理带分隔符的格式 (YYYY-MM 或 YYYY/MM)
                        if (value.length >= 6) {
                            monthStr = value.substring(5);
                            // 如果有第二个分隔符，只取分隔符前的部分
                            const secondSeparatorIndex = monthStr.indexOf('-') !== -1 ?
                                monthStr.indexOf('-') : monthStr.indexOf('/');
                            if (secondSeparatorIndex !== -1) {
                                monthStr = monthStr.substring(0, secondSeparatorIndex);
                            }
                        }
                    } else {
                        // 处理无分隔符的格式 (YYYYMM)
                        monthStr = value.substring(4);
                        if (monthStr.length > 2) {
                            monthStr = monthStr.substring(0, 2);
                        }
                    }

                    if (monthStr && monthStr.length > 0) {
                        month = parseInt(monthStr, 10);
                        if (isNaN(month) || month < 1 || month > 12) {
                            month = 1;
                        }
                    }

                    // 尝试匹配日期
                    if (hasSeparator) {
                        // 查找第二个分隔符
                        const secondSeparatorIndex = value.indexOf('-', 5) !== -1 ?
                            value.indexOf('-', 5) : value.indexOf('/', 5);

                        if (secondSeparatorIndex !== -1 && value.length > secondSeparatorIndex) {
                            // 有第二个分隔符，提取日期部分
                            const dayStr = value.substring(secondSeparatorIndex + 1);
                            if (dayStr.length > 0) {
                                day = parseInt(dayStr, 10);
                                const daysInMonth = new Date(year, month, 0).getDate();
                                if (isNaN(day) || day < 1 || day > daysInMonth) {
                                    day = 1;
                                }
                            }
                        }
                    } else if (value.length >= 6) {
                        // 处理无分隔符的格式 (YYYYMM 或 YYYYMMDD)
                        if (value.length >= 8) {
                            // 完整的 YYYYMMDD 格式
                            const dayStr = value.substring(6);
                            if (dayStr.length > 0) {
                                day = parseInt(dayStr, 10);
                                const daysInMonth = new Date(year, month, 0).getDate();
                                if (isNaN(day) || day < 1 || day > daysInMonth) {
                                    day = 1;
                                }
                            }
                        } else if (value.length === 6 || value.length === 7) {
                            // YYYYMM 格式，日期默认为1
                            day = 1;
                        }
                    }
                }

                // 创建日期对象并更新表单
                const date = moment([year, month - 1, day]);

                // 更新表单字段 - 使用moment对象，并触发表单验证
                formInstance.setFieldsValue({ [fieldName]: date });

                // 手动触发表单字段变化事件，确保表单状态正确更新
                formInstance.validateFields([fieldName]).catch(() => {});

                // 在控制台显示匹配的日期（用于调试）
                console.log(`匹配日期: ${year}-${month}-${day}, 字段: ${fieldName}, moment对象:`, date.format('YYYY-MM-DD'));
            }
        }
    };


    // 岗位选项（与员工表单保持一致）
    const positionOptions = [
        { value: '行政管理', label: '行政管理' },
        { value: '技术工程师', label: '技术工程师' },
        { value: '项目经理', label: '项目经理' },
        { value: '项目代表', label: '项目代表' },
        { value: '项目助理', label: '项目助理' },
        { value: '项目管理', label: '项目管理' },
        { value: '业务经理', label: '业务经理' },
        { value: '品控工程师', label: '品控工程师' },
        { value: '采购工程师', label: '采购工程师' },
        { value: '采购助理', label: '采购助理' },
        { value: '外贸专员', label: '外贸专员' },
        { value: '出纳/会计', label: '出纳/会计' },
        { value: '行政助理', label: '行政助理' },
        { value: '其他', label: '其他' }
    ];

    // 部门选项（与员工表单保持一致）
    const departments = [
        {
            value: '工程部',
            label: '工程部',
            subOptions: [
                { value: '技术科', label: '技术科' },
                { value: '项目管理科', label: '项目管理科' }
            ]
        },
        {
            value: '商贸部',
            label: '商贸部',
        },
        {
            value: '供应管理部',
            label: '供应管理部',
        },
        {
            value: '财务部',
            label: '财务部'
        },
        {
            value: '行政部',
            label: '行政部',
            subOptions: [
                { value: '行政管理', label: '行政管理' },
                { value: '综合办', label: '综合办' }
            ]
        }
    ];

    // 生成部门选项列表（包含子部门）
    const getDepartmentOptions = () => {
        const options = [];
        departments.forEach(dept => {
            options.push({ value: dept.value, label: dept.label });
            if (dept.subOptions) {
                dept.subOptions.forEach(subDept => {
                    options.push({
                        value: `${dept.value}-${subDept.value}`,
                        label: `${dept.label} - ${subDept.label}`
                    });
                });
            }
        });
        return options;
    };

    // 获取所有试用期员工
    const fetchProbationEmployees = async () => {
        setLoading(true);
        try {
            const response = await fetch(`${config.apiBaseUrl}/api/probation/employees`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('获取试用期员工数据失败');
            }

            const result = await response.json();
            // 新API返回的数据结构是 { success: true, data: [...] }
            const data = result.success ? result.data : result;
            setProbationEmployees(data);
        } catch (error) {
            console.error('获取试用期员工失败:', error);
            message.error('获取试用期员工数据失败');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (visible) {
            fetchProbationEmployees();
            // 居中显示
            centerPosition();
        }
    }, [visible, centerPosition]);

    // 添加拖动事件监听器
    useEffect(() => {
        if (isDragging) {
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);

            return () => {
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
            };
        }
    }, [isDragging, handleMouseMove, handleMouseUp]);

    // 窗口大小变化时重新居中
    useEffect(() => {
        const handleResize = () => {
            if (visible) {
                centerPosition();
            }
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [visible, centerPosition]);

    // 组件卸载时清理状态
    useEffect(() => {
        return () => {
            // 清理所有状态，防止内存泄漏
            setConfirmModalVisible(false);
            setSelectedEmployee(null);
            setIsSubmitting(false);
            form.resetFields();
        };
    }, [form]);

    // 计算剩余天数
    const calculateRemainingDays = (endDate) => {
        const today = moment();
        const end = moment(endDate);
        return end.diff(today, 'days');
    };

    // 获取状态标签
    const getStatusTag = (employee) => {
        // 优先使用新API返回的状态信息
        if (employee.probationStatus && employee.daysInfo) {
            switch (employee.probationStatus) {
                case 'expired':
                    return <Tag color="red" icon={<ExclamationCircleOutlined />}>{employee.daysInfo}</Tag>;
                case 'upcoming':
                    return <Tag color="orange" icon={<ClockCircleOutlined />}>{employee.daysInfo}</Tag>;
                case 'normal':
                default:
                    return <Tag color="green" icon={<CheckCircleOutlined />}>{employee.daysInfo}</Tag>;
            }
        }

        // 兼容旧的计算方式（作为备用）
        const remainingDays = calculateRemainingDays(employee.probationEndDate);

        if (remainingDays < 0) {
            return <Tag color="red" icon={<ExclamationCircleOutlined />}>已超期 {Math.abs(remainingDays)} 天</Tag>;
        } else if (remainingDays === 0) {
            return <Tag color="orange" icon={<ClockCircleOutlined />}>今日到期</Tag>;
        } else if (remainingDays <= 7) {
            return <Tag color="gold" icon={<ClockCircleOutlined />}>还有 {remainingDays} 天到期</Tag>;
        } else {
            return <Tag color="green" icon={<CheckCircleOutlined />}>还有 {remainingDays} 天到期</Tag>;
        }
    };

    // 处理转正确认
    const handleConfirmRegularization = (employee) => {
        setSelectedEmployee(employee);

        // 处理部门显示值
        let departmentValue = employee.department;
        if (employee.subDepartment) {
            departmentValue = `${employee.department}-${employee.subDepartment}`;
        }

        form.setFieldsValue({
            regularizationDate: moment(),
            newPosition: employee.position || '',
            newDepartment: departmentValue,
            remarks: ''
        });
        setConfirmModalVisible(true);
    };

    // 处理拒绝转正
    const handleRejectRegularization = (employee) => {
        setSelectedEmployee(employee);
        rejectForm.setFieldsValue({
            reason: ''
        });
        setRejectModalVisible(true);
    };

    // 处理待定
    const handlePendingRegularization = (employee) => {
        setSelectedEmployee(employee);
        pendingForm.setFieldsValue({
            reason: '',
            pendingUntil: moment().add(7, 'days') // 默认延期7天
        });
        setPendingModalVisible(true);
    };

    // 处理取消转正确认
    const handleCancelRegularization = () => {
        try {
            // 防止在loading状态下关闭
            if (loading) {
                return;
            }

            // 立即关闭弹窗，避免用户重复点击
            setConfirmModalVisible(false);

            // 延迟重置其他状态，确保弹窗完全关闭
            setTimeout(() => {
                form.resetFields();
                setSelectedEmployee(null);
            }, 100);
        } catch (error) {
            console.error('关闭弹窗时出错:', error);
            // 强制重置所有状态
            setConfirmModalVisible(false);
            setSelectedEmployee(null);
            form.resetFields();
        }
    };

    // 提交转正确认
    const submitRegularization = async () => {
        // 防止重复提交
        if (isSubmitting || loading) {
            return;
        }

        try {
            const values = await form.validateFields();
            setIsSubmitting(true);
            setLoading(true);

            // 解析部门和子部门
            let newDepartment = values.newDepartment;
            let newSubDepartment = '';

            if (values.newDepartment && values.newDepartment.includes('-')) {
                const parts = values.newDepartment.split('-');
                newDepartment = parts[0];
                newSubDepartment = parts[1];
            }

            // 添加超时处理
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

            try {
                const response = await fetch(`${config.apiBaseUrl}/employees/${selectedEmployee._id}/regularization`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        regularizationDate: values.regularizationDate.format('YYYY-MM-DD'), // moment 对象
                        newPosition: values.newPosition,
                        newDepartment: newDepartment,
                        newSubDepartment: newSubDepartment,
                        remarks: values.remarks
                    }),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.message || '转正确认失败');
                }

                message.success(`${selectedEmployee.name} 转正确认成功！`);

                // 先关闭弹窗
                setConfirmModalVisible(false);

                // 延迟重置状态，确保弹窗完全关闭
                setTimeout(() => {
                    form.resetFields();
                    setSelectedEmployee(null);
                }, 100);

                // 刷新数据 - 使用 setTimeout 避免与状态重置冲突
                setTimeout(async () => {
                    try {
                        await fetchProbationEmployees();
                        if (onRefresh) {
                            onRefresh();
                        }
                    } catch (refreshError) {
                        console.error('刷新数据失败:', refreshError);
                    }
                }, 200);
            } catch (fetchError) {
                clearTimeout(timeoutId);
                if (fetchError.name === 'AbortError') {
                    throw new Error('请求超时，请检查网络连接后重试');
                }
                throw fetchError;
            }
        } catch (error) {
            console.error('转正确认失败:', error);
            if (error.message.includes('超时')) {
                message.error('操作超时，请检查网络连接后重试');
            } else {
                message.error(error.message || '转正确认失败，请重试');
            }
        } finally {
            setLoading(false);
            setIsSubmitting(false);
        }
    };

    // 提交拒绝转正
    const submitRejectRegularization = async () => {
        if (isSubmitting || loading) return;

        let values;
        try {
            values = await rejectForm.validateFields();
        } catch (error) {
            // 表单验证失败，不做任何处理，让表单自己显示验证错误
            console.log('表单验证失败:', error);
            return;
        }

        try {
            setIsSubmitting(true);
            setLoading(true);

            const response = await fetch(`${config.apiBaseUrl}/employees/${selectedEmployee._id}/reject-regularization`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    reason: values.reason
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || '拒绝转正失败');
            }

            message.success(`${selectedEmployee.name} 拒绝转正处理成功！`);
            setRejectModalVisible(false);
            rejectForm.resetFields();
            setSelectedEmployee(null);

            setTimeout(async () => {
                try {
                    await fetchProbationEmployees();
                    if (onRefresh) onRefresh();
                } catch (refreshError) {
                    console.error('刷新数据失败:', refreshError);
                }
            }, 200);
        } catch (error) {
            console.error('拒绝转正失败:', error);
            message.error(error.message || '拒绝转正失败，请重试');
        } finally {
            setLoading(false);
            setIsSubmitting(false);
        }
    };

    // 提交待定处理
    const submitPendingRegularization = async () => {
        if (isSubmitting || loading) return;

        let values;
        try {
            values = await pendingForm.validateFields();
        } catch (error) {
            // 表单验证失败，不做任何处理，让表单自己显示验证错误
            console.log('表单验证失败:', error);
            return;
        }

        try {
            setIsSubmitting(true);
            setLoading(true);

            const response = await fetch(`${config.apiBaseUrl}/employees/${selectedEmployee._id}/pending-regularization`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    reason: values.reason,
                    pendingUntil: values.pendingUntil.format('YYYY-MM-DD') // moment 对象
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || '待定处理失败');
            }

            message.success(`${selectedEmployee.name} 待定处理成功！`);
            setPendingModalVisible(false);
            pendingForm.resetFields();
            setSelectedEmployee(null);

            setTimeout(async () => {
                try {
                    await fetchProbationEmployees();
                    if (onRefresh) onRefresh();
                } catch (refreshError) {
                    console.error('刷新数据失败:', refreshError);
                }
            }, 200);
        } catch (error) {
            console.error('待定处理失败:', error);
            message.error(error.message || '待定处理失败，请重试');
        } finally {
            setLoading(false);
            setIsSubmitting(false);
        }
    };

    const columns = [
        {
            title: '工号',
            dataIndex: 'employeeId',
            key: 'employeeId',
            width: 60,
            fixed: 'left'
        },
        {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            width: 80,
            fixed: 'left'
        },
        {
            title: '所属部门',
            dataIndex: 'department',
            key: 'department',
            width: 130,
            render: (text, record) => {
                if (record.subDepartment) {
                    return `${text} - ${record.subDepartment}`;
                }
                return text;
            }
        },
        {
            title: '岗位名称',
            dataIndex: 'position',
            key: 'position',
            width: 90
        },
        {
            title: '工作性质',
            dataIndex: 'workType',
            key: 'workType',
            width: 70
        },
        {
            title: '入职日期',
            dataIndex: 'entryDate',
            key: 'entryDate',
            width: 100
        },
        {
            title: '试用期截止',
            dataIndex: 'probationEndDate',
            key: 'probationEndDate',
            width: 100
        },
        {
            title: '状态',
            key: 'status',
            width: 130,
            render: (_, employee) => getStatusTag(employee)
        },
        {
            title: '备注',
            key: 'remarks',
            width: 120,
            render: (_, employee) => {
                // 检查是否是被延期的员工
                if (employee.regularizationRemarks && employee.regularizationRemarks.includes('转正待定')) {
                    return <Tag color="orange" icon={<ClockCircleOutlined />}>延期待定</Tag>;
                }
                return '-';
            }
        },
        {
            title: '操作',
            key: 'actions',
            width: 200,
            fixed: 'right',
            render: (_, employee) => (
                <div className="action-buttons-container">
                    <span
                        className="text-link-common success"
                        onClick={() => handleConfirmRegularization(employee)}
                    >
                        <CheckCircleOutlined style={{ marginRight: '4px' }} />
                        转正
                    </span>
                    <span
                        className="text-link-common danger"
                        onClick={() => handleRejectRegularization(employee)}
                    >
                        <CloseCircleOutlined style={{ marginRight: '4px' }} />
                        拒绝
                    </span>
                    <span
                        className="text-link-common"
                        onClick={() => handlePendingRegularization(employee)}
                    >
                        <ClockCircleOutlined style={{ marginRight: '4px' }} />
                        待定
                    </span>
                </div>
            )
        }
    ];

    return (
        <>
            {visible && (
                <div
                    className="custom-modal-overlay"
                    style={{
                        position: 'fixed',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        backgroundColor: 'rgba(0, 0, 0, 0.45)',
                        zIndex: 1000,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}
                    onClick={(e) => {
                        if (e.target === e.currentTarget) {
                            onClose();
                        }
                    }}
                >
                    <div
                        className="custom-modal-container"
                        style={{
                            position: 'absolute',
                            left: position.x,
                            top: position.y,
                            width: 800,
                            backgroundColor: '#fff',
                            borderRadius: 6,
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                            overflow: 'hidden',
                            cursor: isDragging ? 'grabbing' : 'default'
                        }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* 标题栏 */}
                        <div
                            className="custom-modal-header"
                            style={{
                                padding: '16px 24px',
                                borderBottom: '1px solid #f0f0f0',
                                backgroundColor: '#fafafa',
                                cursor: 'grab',
                                userSelect: 'none',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between'
                            }}
                            onMouseDown={handleMouseDown}
                        >
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                <ExclamationCircleOutlined style={{ color: '#faad14', marginRight: 8 }} />
                                试用期员工管理
                                <span style={{ marginLeft: 8, fontSize: '12px', color: '#999', fontWeight: 'normal' }}>
                                    (可拖动)
                                </span>
                            </div>
                            <Button
                                type="text"
                                size="small"
                                onClick={onClose}
                                style={{
                                    border: 'none',
                                    boxShadow: 'none',
                                    fontSize: '16px',
                                    width: 22,
                                    height: 22,
                                    padding: 0,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center'
                                }}
                            >
                                ×
                            </Button>
                        </div>

                        {/* 内容区域 */}
                        <div style={{ padding: 24 }}>
                            <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f0f9ff', borderRadius: 6, border: '1px solid #bae7ff' }}>
                                <p style={{ color: '#666', margin: '0' }}>
                                    以下是所有试用期员工的详细信息，您可以查看状态并进行转正、延期等操作：
                                </p>
                            </div>

                            <Table
                                columns={columns}
                                dataSource={probationEmployees}
                                rowKey="_id"
                                loading={loading}
                                pagination={false}
                                scroll={{ x: 800, y: 400 }}
                                size="small"
                            />

                            {probationEmployees.length === 0 && !loading && (
                                <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                                    <CheckCircleOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                                    <p>暂无即将到期的试用期员工</p>
                                </div>
                            )}
                        </div>

                        {/* 底部按钮 */}
                        <div style={{
                            padding: '16px 24px',
                            borderTop: '1px solid #f0f0f0',
                            textAlign: 'right'
                        }}>
                            <button
                                onClick={onClose}
                                className="back-button"
                                style={{
                                    height: '32px',
                                    lineHeight: '32px',
                                    padding: '0 15px',
                                    border: 'none',
                                    borderRadius: '6px',
                                    fontSize: '13px',
                                    fontWeight: '500',
                                    cursor: 'pointer',
                                    transition: 'all 0.2s ease',
                                    color: 'white',
                                    whiteSpace: 'nowrap',
                                    minWidth: '90px',
                                    width: 'auto',
                                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                    backgroundColor: '#3b82f6'
                                }}
                                onMouseEnter={(e) => e.target.style.backgroundColor = '#2563eb'}
                                onMouseLeave={(e) => e.target.style.backgroundColor = '#3b82f6'}
                            >
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* 转正确认弹窗 */}
            <Modal
                title="确认员工转正"
                open={confirmModalVisible}
                onCancel={handleCancelRegularization}
                width={600}
                className="probation-regularization-modal"
                maskClosable={!loading && !isSubmitting}
                closable={!loading && !isSubmitting}
                destroyOnClose={true}
                forceRender={false}
                footer={
                    <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
                        <Button
                            onClick={handleCancelRegularization}
                            disabled={loading || isSubmitting}
                        >
                            取消
                        </Button>
                        <Button
                            type="primary"
                            onClick={submitRegularization}
                            loading={loading || isSubmitting}
                            disabled={isSubmitting}
                        >
                            确认转正
                        </Button>
                    </div>
                }
            >
                {selectedEmployee && (
                    <Form
                        form={form}
                        layout="vertical"
                    >
                        <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                            <p><strong>员工信息：</strong>{selectedEmployee.name} ({selectedEmployee.employeeId})</p>
                            <p><strong>当前部门：</strong>{selectedEmployee.department}</p>
                            <p><strong>当前岗位：</strong>{selectedEmployee.position}</p>
                            <p><strong>试用期截止：</strong>{selectedEmployee.probationEndDate}</p>
                        </div>

                        <div style={{ display: 'flex', gap: '16px' }}>
                            <Form.Item
                                name="regularizationDate"
                                label="转正日期"
                                rules={[{ required: true, message: '请选择转正日期' }]}
                                style={{ flex: 1 }}
                                getValueFromEvent={(date) => {
                                    console.log('getValueFromEvent called with:', date);
                                    return date ? moment(date) : null;
                                }}
                                getValueProps={(value) => {
                                    console.log('getValueProps called with:', value);
                                    return {
                                        selected: value && moment.isMoment(value) ? value.toDate() : null
                                    };
                                }}
                            >
                                <DatePicker
                                    dateFormat="yyyy-MM-dd"
                                    placeholderText="请选择转正日期"
                                    className="form-control"
                                    shouldCloseOnSelect={true}
                                    showPopperArrow={false}
                                    onClickOutside={() => {}}
                                    onChangeRaw={(e) => {
                                        const value = e.target.value;
                                        if (value) {
                                            handleDateInputMatch(value, 'regularizationDate', form);
                                        }
                                    }}
                                    wrapperClassName="custom-datepicker-wrapper"
                                    popperProps={{
                                        positionFixed: true,
                                        modifiers: {
                                            preventOverflow: {
                                                enabled: true,
                                                escapeWithReference: false,
                                                boundariesElement: 'viewport'
                                            }
                                        }
                                    }}
                                    style={{ width: '100%' }}
                                />
                            </Form.Item>

                            <Form.Item
                                name="newPosition"
                                label="转正后岗位"
                                rules={[{ required: true, message: '请选择转正后岗位' }]}
                                style={{ flex: 1 }}
                            >
                                <Select placeholder="请选择转正后岗位" showSearch>
                                    {positionOptions.map(option => (
                                        <Option key={option.value} value={option.value}>
                                            {option.label}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>

                            <Form.Item
                                name="newDepartment"
                                label="转正后部门"
                                rules={[{ required: true, message: '请选择转正后部门' }]}
                                style={{ flex: 1 }}
                            >
                                <Select placeholder="请选择转正后部门" showSearch>
                                    {getDepartmentOptions().map(option => (
                                        <Option key={option.value} value={option.value}>
                                            {option.label}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </div>

                        <Form.Item
                            name="remarks"
                            label="备注"
                        >
                            <TextArea
                                rows={3}
                                placeholder="请输入转正备注信息（可选）"
                                maxLength={200}
                                showCount={false}
                            />
                        </Form.Item>
                        <div style={{ textAlign: 'right', color: '#999', fontSize: '12px', marginTop: '-16px', marginBottom: '16px' }}>
                            最多200个字符
                        </div>
                    </Form>
                )}
            </Modal>

            {/* 拒绝转正弹窗 */}
            <Modal
                title="拒绝员工转正"
                open={rejectModalVisible}
                onCancel={() => {
                    setRejectModalVisible(false);
                    rejectForm.resetFields();
                    setSelectedEmployee(null);
                }}
                width={500}
                className="probation-regularization-modal"
                maskClosable={!loading && !isSubmitting}
                closable={!loading && !isSubmitting}
                destroyOnClose={true}
                footer={
                    <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
                        <Button
                            onClick={() => {
                                setRejectModalVisible(false);
                                rejectForm.resetFields();
                                setSelectedEmployee(null);
                            }}
                            disabled={loading || isSubmitting}
                        >
                            取消
                        </Button>
                        <Button
                            type="primary"
                            danger
                            onClick={submitRejectRegularization}
                            loading={loading || isSubmitting}
                            disabled={isSubmitting}
                        >
                            确认拒绝
                        </Button>
                    </div>
                }
            >
                {selectedEmployee && (
                    <Form
                        form={rejectForm}
                        layout="vertical"
                    >
                        <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                            <p><strong>员工信息：</strong>{selectedEmployee.name} ({selectedEmployee.employeeId})</p>
                            <p><strong>当前部门：</strong>{selectedEmployee.department}</p>
                            <p><strong>当前岗位：</strong>{selectedEmployee.position}</p>
                            <p><strong>试用期截止：</strong>{selectedEmployee.probationEndDate}</p>
                        </div>

                        <Form.Item
                            name="reason"
                            label="拒绝理由"
                            rules={[{ required: true, message: '请输入拒绝理由' }]}
                        >
                            <TextArea
                                rows={4}
                                placeholder="请详细说明拒绝转正的理由..."
                                maxLength={500}
                                showCount={false}
                            />
                        </Form.Item>
                        <div style={{ textAlign: 'right', color: '#999', fontSize: '12px', marginTop: '-16px', marginBottom: '16px' }}>
                            最多500个字符
                        </div>
                    </Form>
                )}
            </Modal>

            {/* 待定处理弹窗 */}
            <Modal
                title="员工转正待定"
                open={pendingModalVisible}
                onCancel={() => {
                    setPendingModalVisible(false);
                    pendingForm.resetFields();
                    setSelectedEmployee(null);
                }}
                width={600}
                className="probation-regularization-modal"
                maskClosable={!loading && !isSubmitting}
                closable={!loading && !isSubmitting}
                destroyOnClose={true}
                footer={
                    <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
                        <Button
                            onClick={() => {
                                setPendingModalVisible(false);
                                pendingForm.resetFields();
                                setSelectedEmployee(null);
                            }}
                            disabled={loading || isSubmitting}
                        >
                            取消
                        </Button>
                        <Button
                            type="primary"
                            onClick={submitPendingRegularization}
                            loading={loading || isSubmitting}
                            disabled={isSubmitting}
                        >
                            确认待定
                        </Button>
                    </div>
                }
            >
                {selectedEmployee && (
                    <Form
                        form={pendingForm}
                        layout="vertical"
                    >
                        <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                            <p><strong>员工信息：</strong>{selectedEmployee.name} ({selectedEmployee.employeeId})</p>
                            <p><strong>当前部门：</strong>{selectedEmployee.department}</p>
                            <p><strong>当前岗位：</strong>{selectedEmployee.position}</p>
                            <p><strong>试用期截止：</strong>{selectedEmployee.probationEndDate}</p>
                        </div>

                        <div style={{ display: 'flex', gap: '16px' }}>
                            <Form.Item
                                name="pendingUntil"
                                label="延期至"
                                rules={[{ required: true, message: '请选择延期日期' }]}
                                style={{ flex: 1 }}
                                getValueFromEvent={(date) => {
                                    console.log('pendingUntil getValueFromEvent called with:', date);
                                    return date ? moment(date) : null;
                                }}
                                getValueProps={(value) => {
                                    console.log('pendingUntil getValueProps called with:', value);
                                    return {
                                        selected: value && moment.isMoment(value) ? value.toDate() : null
                                    };
                                }}
                            >
                                <DatePicker
                                    dateFormat="yyyy-MM-dd"
                                    placeholderText="请选择延期日期"
                                    className="form-control"
                                    shouldCloseOnSelect={true}
                                    showPopperArrow={false}
                                    onClickOutside={() => {}}
                                    onChangeRaw={(e) => {
                                        const value = e.target.value;
                                        if (value) {
                                            handleDateInputMatch(value, 'pendingUntil', pendingForm);
                                        }
                                    }}
                                    wrapperClassName="custom-datepicker-wrapper"
                                    minDate={new Date()} // 禁用今天之前的日期
                                    popperProps={{
                                        positionFixed: true,
                                        modifiers: {
                                            preventOverflow: {
                                                enabled: true,
                                                escapeWithReference: false,
                                                boundariesElement: 'viewport'
                                            }
                                        }
                                    }}
                                    style={{ width: '100%' }}
                                />
                            </Form.Item>
                        </div>

                        <Form.Item
                            name="reason"
                            label="待定理由"
                            rules={[{ required: true, message: '请输入待定理由' }]}
                        >
                            <TextArea
                                rows={3}
                                placeholder="请说明待定的理由和后续安排..."
                                maxLength={300}
                                showCount={false}
                            />
                        </Form.Item>
                        <div style={{ textAlign: 'right', color: '#999', fontSize: '12px', marginTop: '-16px', marginBottom: '16px' }}>
                            最多300个字符
                        </div>
                    </Form>
                )}
            </Modal>
        </>
    );
};

export default ProbationReminder;
