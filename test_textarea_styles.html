<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>备注输入框样式优化对比</title>
    <link href="https://cdn.jsdelivr.net/npm/antd@5.0.0/dist/reset.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .comparison-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-section {
            flex: 1;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .comparison-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        .form-item {
            margin-bottom: 20px;
        }
        .form-item label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        .textarea {
            width: 100%;
            min-height: 80px;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            transition: border-color 0.3s;
        }
        .textarea:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        .char-count {
            text-align: right;
            color: #999;
            font-size: 12px;
            margin-top: 4px;
        }
        .antd-count {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 4px;
            font-size: 12px;
            color: #999;
        }
        .problem {
            background-color: #fff2f0;
            border-left: 4px solid #ff4d4f;
            padding: 10px;
            margin: 10px 0;
        }
        .solution {
            background-color: #f6ffed;
            border-left: 4px solid #52c41a;
            padding: 10px;
            margin: 10px 0;
        }
        .modal-demo {
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            background: #fafafa;
        }
        .modal-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .employee-info {
            background: #f5f5f5;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 16px;
            font-size: 14px;
        }
        .footer-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
            margin-top: 20px;
        }
        .btn {
            padding: 6px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }
        .btn-danger {
            background: #ff4d4f;
            border-color: #ff4d4f;
            color: white;
        }
    </style>
</head>
<body>
    <h1>备注输入框样式优化对比</h1>
    
    <div class="problem">
        <h4>🚨 问题描述</h4>
        <p>拒绝和延期弹窗的备注输入框存在最多字符提示重复的问题，与转正备注输入框样式不一致。</p>
        <ul>
            <li>拒绝理由输入框：使用 <code>showCount</code> 显示 Ant Design 自带计数</li>
            <li>延期理由输入框：使用 <code>showCount</code> 显示 Ant Design 自带计数</li>
            <li>转正备注输入框：使用 <code>showCount={false}</code> + 自定义提示</li>
        </ul>
    </div>

    <div class="comparison-container">
        <div class="comparison-section">
            <h3>❌ 修复前（有问题的样式）</h3>
            
            <div class="modal-demo">
                <div class="modal-title">拒绝员工转正</div>
                <div class="employee-info">
                    <strong>员工信息：</strong>张三 (M001)<br>
                    <strong>当前部门：</strong>工程部<br>
                    <strong>当前岗位：</strong>技术员
                </div>
                <div class="form-item">
                    <label>拒绝理由 *</label>
                    <textarea 
                        class="textarea" 
                        placeholder="请详细说明拒绝转正的理由..."
                        maxlength="500"
                        oninput="updateCount(this, 'old-reject-count')"
                    ></textarea>
                    <div class="antd-count">
                        <span></span>
                        <span id="old-reject-count">0 / 500</span>
                    </div>
                </div>
                <div class="footer-buttons">
                    <button class="btn">取消</button>
                    <button class="btn btn-danger">确认拒绝</button>
                </div>
            </div>

            <div class="modal-demo">
                <div class="modal-title">员工转正待定</div>
                <div class="employee-info">
                    <strong>员工信息：</strong>李四 (M002)<br>
                    <strong>当前部门：</strong>商贸部<br>
                    <strong>当前岗位：</strong>销售员
                </div>
                <div class="form-item">
                    <label>待定理由 *</label>
                    <textarea 
                        class="textarea" 
                        placeholder="请说明待定的理由和后续安排..."
                        maxlength="300"
                        oninput="updateCount(this, 'old-pending-count')"
                    ></textarea>
                    <div class="antd-count">
                        <span></span>
                        <span id="old-pending-count">0 / 300</span>
                    </div>
                </div>
                <div class="footer-buttons">
                    <button class="btn">取消</button>
                    <button class="btn btn-primary">确认待定</button>
                </div>
            </div>
        </div>

        <div class="comparison-section">
            <h3>✅ 修复后（统一样式）</h3>
            
            <div class="modal-demo">
                <div class="modal-title">拒绝员工转正</div>
                <div class="employee-info">
                    <strong>员工信息：</strong>张三 (M001)<br>
                    <strong>当前部门：</strong>工程部<br>
                    <strong>当前岗位：</strong>技术员
                </div>
                <div class="form-item">
                    <label>拒绝理由 *</label>
                    <textarea 
                        class="textarea" 
                        placeholder="请详细说明拒绝转正的理由..."
                        maxlength="500"
                    ></textarea>
                    <div class="char-count">最多500个字符</div>
                </div>
                <div class="footer-buttons">
                    <button class="btn">取消</button>
                    <button class="btn btn-danger">确认拒绝</button>
                </div>
            </div>

            <div class="modal-demo">
                <div class="modal-title">员工转正待定</div>
                <div class="employee-info">
                    <strong>员工信息：</strong>李四 (M002)<br>
                    <strong>当前部门：</strong>商贸部<br>
                    <strong>当前岗位：</strong>销售员
                </div>
                <div class="form-item">
                    <label>待定理由 *</label>
                    <textarea 
                        class="textarea" 
                        placeholder="请说明待定的理由和后续安排..."
                        maxlength="300"
                    ></textarea>
                    <div class="char-count">最多300个字符</div>
                </div>
                <div class="footer-buttons">
                    <button class="btn">取消</button>
                    <button class="btn btn-primary">确认待定</button>
                </div>
            </div>

            <div class="modal-demo">
                <div class="modal-title">确认员工转正</div>
                <div class="employee-info">
                    <strong>员工信息：</strong>王五 (M003)<br>
                    <strong>当前部门：</strong>财务部<br>
                    <strong>当前岗位：</strong>会计
                </div>
                <div class="form-item">
                    <label>备注</label>
                    <textarea 
                        class="textarea" 
                        placeholder="请输入转正备注信息（可选）"
                        maxlength="200"
                    ></textarea>
                    <div class="char-count">最多200个字符</div>
                </div>
                <div class="footer-buttons">
                    <button class="btn">取消</button>
                    <button class="btn btn-primary">确认转正</button>
                </div>
            </div>
        </div>
    </div>

    <div class="solution">
        <h4>✅ 解决方案</h4>
        <p>统一所有备注输入框的样式，使用相同的字符提示方式：</p>
        <ul>
            <li>设置 <code>showCount={false}</code> 禁用 Ant Design 自带计数</li>
            <li>添加自定义字符提示：<code>&lt;div style={{...}}&gt;最多XXX个字符&lt;/div&gt;</code></li>
            <li>保持一致的样式：右对齐、灰色文字、12px字体、4px上边距</li>
        </ul>
    </div>

    <script>
        function updateCount(textarea, countId) {
            const current = textarea.value.length;
            const max = textarea.maxLength;
            document.getElementById(countId).textContent = `${current} / ${max}`;
        }
    </script>
</body>
</html>
